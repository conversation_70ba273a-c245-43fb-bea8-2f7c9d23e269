# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Start Flask development server
python appstore-reviews-extraction/app.py
# Server runs on http://localhost:5000

# Command-line review extraction to CSV
python appstore-reviews-extraction/meralco_app_reviews.py "<start_date>" "<end_date>"
# Date format: MMM DD, YYYY (e.g., "Oct 1, 2024")
```

### Environment Setup
```bash
# Using pip
pip install -r appstore-reviews-extraction/requirements.txt

# Using conda
conda env create -f appstore-reviews-extraction/environment.yml
conda activate meralco_analysis
```

## Architecture Overview

### Core Components
- **Flask Backend (`app.py`)**: Web server providing REST API and real-time data streaming via Server-Sent Events
- **CLI Tool (`meralco_app_reviews.py`)**: Standalone script for batch review extraction to CSV format  
- **Frontend (`templates/index.html`)**: Single-page application with interactive forms, real-time progress, and Chart.js visualizations
- **Styling (`static/css/styles.css`)**: Responsive design with dark/light theme switching and localStorage persistence

### Data Flow Architecture
1. **Review Fetching**: iTunes RSS API integration with multi-country support and pagination
2. **Real-time Processing**: Server-sent events stream progress updates during data collection
3. **Data Pipeline**: Parse → Filter (date/rating/keywords) → Analyze → Visualize → Export
4. **Visualization**: Chart.js for rating distribution and timeline charts
5. **Export**: Excel (.xlsx) and CSV format support

### Key Technical Details
- **Review Data Structure**: ID, title, content, rating (1-5), author, app version, date
- **Multi-Country Support**: US, Philippines, UK, Canada, Australia with "All Countries" option
- **Filtering System**: Date range, rating range (min/max), keyword search (comma-separated)
- **Real-time Updates**: Progress bar and streaming data updates via EventSource API
- **Theme System**: Dark/light mode toggle with localStorage persistence

### iTunes RSS API Integration
- **Endpoint Pattern**: `https://itunes.apple.com/{country}/rss/customerreviews/page={page}/id={app_id}/sortby=mostrecent/json`
- **Pagination**: Up to 10 pages per country (configurable via MAX_PAGES)
- **Rate Limiting**: Sequential requests to avoid API throttling
- **Error Handling**: HTTP status checks and JSON parsing with fallback behavior

## File Structure Context

```
appstore-reviews-extraction/
├── app.py                  # Main Flask web application
├── meralco_app_reviews.py  # CLI tool for batch processing
├── templates/
│   └── index.html         # Single-page frontend application
├── static/css/
│   └── styles.css         # Responsive styling with theme support
├── requirements.txt       # Python dependencies
├── environment.yml        # Conda environment specification
└── README.md             # Project documentation
```

### Dependencies
- **Flask**: Web framework and routing
- **pandas/numpy**: Data manipulation and analysis
- **requests**: HTTP client for iTunes API
- **xlsxwriter**: Excel file generation
- **Chart.js**: Frontend data visualization (CDN)
- **Optional**: matplotlib, seaborn, plotly, textblob, nltk, scikit-learn for advanced analysis