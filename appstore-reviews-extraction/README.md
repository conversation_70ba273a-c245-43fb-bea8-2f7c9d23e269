# Appstore Reviews Extraction

## Overview
This project analyzes user reviews of mobile applications from the Apple App Store. It aims to provide insights into user sentiment, common issues, and trends in user feedback.

## Features
- Sentiment analysis of user reviews
- Time series analysis of ratings
- Common complaint identification
- Word cloud visualization of review content
- Interactive visualizations of key metrics

## Project Structure

project/
├── templates/
│ └── index.html # Main web interface
├── styles/
│ └── DarkModeSwitch.css # Dark mode styling
├── app.py # Flask web application
├── requirements.txt # Python dependencies
└── README.md # Project documentation

## Installation

### Prerequisites
- Python 3.11 or higher
- pip package manager

### Setup
1. Create virtual environment
bash
python -m venv venv
source venv/bin/activate # On Windows: venv\Scripts\activate

2. Install dependencies
bash
pip install -r requirements.txt

## Running the Application
1. Activate the virtual environment (if not already activated)
2. Run the Flask application:
bash
python app.py

3. Open your browser and navigate to `http://localhost:5000`

## Data Format
The application processes review data in JSON format with the following structure:
json
{
"id": "string", # Unique review identifier
"title": "string", # Review title
"content": "string", # Review content
"rating": integer, # Rating (1-5)
"author": "string", # Reviewer username
"version": "string", # App version
"date": "datetime" # Review date
}


## Key Components

### Web Application (app.py)
- Displays interactive dashboards
- Shows analysis results
- Provides filtering capabilities
- Enables data exploration
- Supports dark/light mode switching

### Frontend
- Responsive web interface (index.html)
- Dark mode support (DarkModeSwitch.css)
- Interactive data visualizations

## Dependencies
- Flask: Web framework
- pandas: Data manipulation
- numpy: Numerical operations
- matplotlib/seaborn: Data visualization
- textblob: Sentiment analysis
- nltk: Text processing
- wordcloud: Word cloud generation
- plotly: Interactive plots

## Contributing
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Submit a pull request
