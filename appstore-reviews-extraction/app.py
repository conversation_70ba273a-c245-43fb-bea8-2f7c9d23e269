import sys
from flask import Flask, render_template, request, jsonify, send_file, url_for
from datetime import datetime, timedelta
import pandas as pd
import io
import requests
import json

print("Python version:", sys.version)
print("Python executable:", sys.executable)
print("Python path:")
for path in sys.path:
    print(path)

app = Flask(__name__)

# Update the COUNTRY_CODES dictionary
COUNTRY_CODES = {
    "All Countries": ["us", "ph", "gb", "ca", "au"],  # List of all country codes
    "United States": "us",
    "Philippines": "ph",
    "United Kingdom": "gb",
    "Canada": "ca",
    "Australia": "au",
}


def fetch_reviews(app_id, country, page=1):
    url = f"https://itunes.apple.com/{country}/rss/customerreviews/page={page}/id={app_id}/sortby=mostrecent/json"
    print(f"Fetching URL: {url}")
    response = requests.get(url)
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Raw response: {json.dumps(data, indent=2)}")
        return data
    else:
        print(f"Error fetching reviews: HTTP {response.status_code}")
        return None


def parse_reviews(data, start_date, end_date, min_rating, max_rating, keywords):
    print(f"Parsing reviews with parameters:")
    print(f"Start date: {start_date}, End date: {end_date}")
    print(f"Min rating: {min_rating}, Max rating: {max_rating}")
    print(f"Keywords: {keywords}")
    reviews = []
    if data and "feed" in data and "entry" in data["feed"]:
        entries = data["feed"]["entry"]
        if isinstance(entries, dict):  # Handle case when there's only one review
            entries = [entries]
        print(f"Processing {len(entries)} entries")
        for entry in entries:
            if "author" in entry:
                try:
                    review_date = datetime.strptime(
                        entry["updated"]["label"], "%Y-%m-%dT%H:%M:%S-07:00"
                    )
                    rating = int(entry["im:rating"]["label"])
                    print(f"Review date: {review_date}, Rating: {rating}")
                    print(f"Date range: {start_date} to {end_date}")
                    if (
                        start_date <= review_date <= end_date
                        and (min_rating is None or rating >= min_rating)
                        and (max_rating is None or rating <= max_rating)
                        and (
                            not keywords
                            or any(
                                k.lower() in entry["content"]["label"].lower()
                                for k in keywords
                            )
                        )
                    ):
                        review = {
                            "id": entry["id"]["label"],
                            "title": entry["title"]["label"],
                            "content": entry["content"]["label"],
                            "rating": rating,
                            "author": entry["author"]["name"]["label"],
                            "version": entry["im:version"]["label"],
                            "date": review_date.strftime("%Y-%m-%d %H:%M:%S"),
                        }
                        reviews.append(review)
                        print("Review added")
                    else:
                        print("Review skipped")
                except KeyError as e:
                    print(f"Error parsing review: {e}")
                except ValueError as e:
                    print(f"Error parsing date: {e}")
    print(f"Parsed {len(reviews)} reviews")
    return reviews


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/get_reviews", methods=["POST"])
def get_reviews():
    print("get_reviews function called")
    app_id = request.form["app_id"]
    start_date = datetime.strptime(request.form["start_date"], "%Y-%m-%d")
    end_date = (
        datetime.strptime(request.form["end_date"], "%Y-%m-%d")
        + timedelta(days=1)
        - timedelta(seconds=1)
    )
    min_rating = int(request.form["min_rating"]) if request.form["min_rating"] else None
    max_rating = int(request.form["max_rating"]) if request.form["max_rating"] else None
    keywords = (
        [k.strip() for k in request.form["keywords"].split(",")]
        if request.form["keywords"]
        else []
    )
    selected_country = request.form["country"]

    if selected_country == "All Countries":
        countries = COUNTRY_CODES["All Countries"]
    else:
        countries = [
            COUNTRY_CODES.get(selected_country, "us")
        ]  # Default to US if not found

    print(f"Fetching reviews for app_id: {app_id}")
    print(f"Date range: {start_date} to {end_date}")
    print(f"Rating range: {min_rating} to {max_rating}")
    print(f"Keywords: {keywords}")
    print(f"Selected country: {selected_country}")
    print(f"Countries to fetch: {countries}")

    def generate():
        all_reviews = []
        total_reviews = 0
        MAX_PAGES = 10

        for country in countries:
            print(f"Fetching reviews for country: {country}")
            for page in range(1, MAX_PAGES + 1):
                print(f"Fetching page {page}")
                data = fetch_reviews(app_id, country, page)
                if data:
                    print(f"Data received for page {page}")
                    reviews = parse_reviews(
                        data, start_date, end_date, min_rating, max_rating, keywords
                    )
                    print(f"Parsed {len(reviews)} reviews from page {page}")
                    all_reviews.extend(reviews)
                    total_reviews += len(reviews)
                    yield f"data: {json.dumps({'progress': True, 'current': total_reviews, 'total': total_reviews})}\n\n"
                    if len(reviews) == 0:
                        print(f"No more reviews found on page {page}")
                        break
                else:
                    print(f"No data received for page {page}")
                    break

        print(f"Total reviews fetched: {len(all_reviews)}")

        if not all_reviews:
            yield f"data: {json.dumps({'error': 'No reviews found matching the criteria'})}\n\n"
        else:
            rating_distribution = {
                i: sum(1 for r in all_reviews if r["rating"] == i) for i in range(1, 6)
            }
            timeline = (
                pd.Series(
                    [
                        datetime.strptime(r["date"], "%Y-%m-%d %H:%M:%S").date()
                        for r in all_reviews
                    ]
                )
                .value_counts()
                .sort_index()
            )
            timeline = {str(date): int(count) for date, count in timeline.items()}

            # Store reviews in app context for Excel export
            app.config["REVIEWS"] = all_reviews

            yield f"data: {json.dumps({'done': True, 'total_reviews': len(all_reviews), 'rating_distribution': rating_distribution, 'timeline': timeline, 'reviews': all_reviews})}\n\n"

    return app.response_class(generate(), mimetype="text/event-stream")


@app.route("/export_excel")
def export_excel():
    reviews = app.config.get("REVIEWS", [])
    if not reviews:
        return "No reviews to export", 400

    df = pd.DataFrame(reviews)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        df.to_excel(writer, sheet_name="Reviews", index=False)

    output.seek(0)
    return send_file(
        output,
        mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        as_attachment=True,
        download_name="reviews.xlsx",
    )


if __name__ == "__main__":
    app.run(debug=True)
