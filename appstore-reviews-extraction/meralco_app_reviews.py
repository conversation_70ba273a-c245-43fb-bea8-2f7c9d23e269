import requests
import json
from datetime import datetime, timedelta
import csv
import sys


def fetch_reviews(app_id, country, page=1):
    url = f"https://itunes.apple.com/{country}/rss/customerreviews/page={page}/id={app_id}/sortby=mostrecent/json"
    try:
        response = requests.get(url)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        return json.loads(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error fetching reviews: {e}")
        return None


def parse_reviews(data, start_date, end_date):
    reviews = []
    if data and "feed" in data and "entry" in data["feed"]:
        entries = data["feed"]["entry"]
        if isinstance(entries, dict):  # Handle case when there's only one review
            entries = [entries]
        for entry in entries:
            if "author" in entry:
                try:
                    review_date = parse_date(entry["updated"]["label"])
                    # Reintroduce the date check
                    if start_date <= review_date <= end_date:
                        review = {
                            "id": entry["id"]["label"],
                            "title": entry["title"]["label"],
                            "content": entry["content"]["label"],
                            "rating": int(entry["im:rating"]["label"]),
                            "author": entry["author"]["name"]["label"],
                            "version": entry["im:version"]["label"],
                            "date": review_date.strftime("%Y-%m-%d %H:%M:%S"),
                        }
                        reviews.append(review)
                except KeyError as e:
                    print(f"Error parsing review: {e}")
                    print(f"Problematic entry: {entry}")
    return reviews


def write_reviews_to_csv(reviews, filename):
    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["id", "title", "content", "rating", "author", "version", "date"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for review in reviews:
            writer.writerow(review)


def parse_date(date_str):
    try:
        return datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        try:
            return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S-07:00")
        except ValueError:
            raise ValueError(f"Unable to parse date: {date_str}")


def main(start_date_str, end_date_str):
    APP_ID = "546535540"  # Updated app ID
    COUNTRY = "ph"  # Philippines
    MAX_PAGES = 10

    start_date = parse_date(start_date_str)
    end_date = parse_date(end_date_str) + timedelta(days=1) - timedelta(seconds=1)

    all_reviews = []

    for page in range(1, MAX_PAGES + 1):
        print(f"Fetching page {page}...")
        data = fetch_reviews(APP_ID, COUNTRY, page)
        if data:
            print(f"Data received for page {page}. Parsing reviews...")
            reviews = parse_reviews(data, start_date, end_date)
            print(f"Reviews parsed on this page: {len(reviews)}")
            if not reviews:
                print(
                    f"No reviews found on page {page} within the specified date range."
                )
                if page == 1:
                    print("Debugging information:")
                    print(
                        f"First entry in data: {json.dumps(data['feed']['entry'][0], indent=2)}"
                    )
                    print(f"Start date: {start_date}, End date: {end_date}")
                # Check if we've gone past the date range
                first_review_date = datetime.strptime(
                    data["feed"]["entry"][0]["updated"]["label"],
                    "%Y-%m-%dT%H:%M:%S-07:00",
                )
                if first_review_date < start_date:
                    print(
                        "Reached reviews older than the specified start date. Stopping."
                    )
                    break
            all_reviews.extend(reviews)
        else:
            print(f"Failed to fetch data for page {page}. Stopping.")
            break

    print(f"Total reviews fetched: {len(all_reviews)}")

    if all_reviews:
        csv_filename = f"meralco_app_reviews_{start_date.strftime('%b_%d_%Y')}_to_{end_date.strftime('%b_%d_%Y')}.csv"
        write_reviews_to_csv(all_reviews, csv_filename)
        print(f"Reviews saved to {csv_filename}")
    else:
        print("No reviews were fetched. The CSV file was not created.")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print('Usage: python meralco_app_reviews.py "<start_date>" "<end_date>"')
        print('Date format: MMM DD, YYYY (e.g., "Oct 1, 2024")')
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    main(start_date, end_date)
