<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apple App Store Review Extractor - AI Labs</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span class="logo-text">App Store <span class="logo-highlight">Analytics</span></span>
            </div>
            <div class="header-controls">
                <div class="theme-switch-wrapper">
                    <label class="theme-switch" for="checkbox">
                        <input type="checkbox" id="checkbox" />
                        <div class="slider round"></div>
                    </label>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Hero Section -->
        <div class="hero-section">
            <h1 class="hero-title">Apple App Store <span class="gradient-text">Review Extractor</span></h1>
            <p class="hero-subtitle">Extract and analyze user reviews from the Apple App Store with advanced filtering, real-time visualization, and comprehensive data export capabilities.</p>
            <div class="hero-buttons">
                <button class="btn-primary" onclick="document.getElementById('review-form-card').scrollIntoView({behavior: 'smooth'})">Extract Reviews</button>
                <button class="btn-secondary" onclick="showLearnMore()">Learn More</button>
            </div>
        </div>
        <!-- Main Form Card -->
        <div class="card glassmorphism" id="review-form-card">
            <div class="card-header">
                <div class="card-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
                    </svg>
                </div>
                <h2 class="card-title">Review Extraction Parameters</h2>
            </div>
            <form id="review-form" method="POST">
                <div class="form-group">
                    <label for="app_id">App ID:</label>
                    <input type="text" id="app_id" name="app_id" required placeholder="e.g., 123456789">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="start_date">Start Date:</label>
                        <input type="date" id="start_date" name="start_date" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="end_date">End Date:</label>
                        <input type="date" id="end_date" name="end_date" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="min_rating">Min Rating:</label>
                        <select id="min_rating" name="min_rating">
                            <option value="">Any</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="max_rating">Max Rating:</label>
                        <select id="max_rating" name="max_rating">
                            <option value="">Any</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="keywords">Keywords (Optional, comma-separated):</label>
                    <input type="text" id="keywords" name="keywords" placeholder="e.g., bug, feature, update">
                </div>
                
                <div class="form-group">
                    <label for="country">Country:</label>
                    <select id="country" name="country">
                        <option value="All Countries">All Countries</option>
                        <option value="United States">United States</option>
                        <option value="Philippines">Philippines</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Canada">Canada</option>
                        <option value="Australia">Australia</option>
                    </select>
                </div>
                
                <button type="submit" class="btn-primary btn-full-width">🚀 Extract Reviews</button>
            </form>
        </div>

        <!-- Progress Card -->
        <div id="progress-container" class="card glassmorphism progress-card" style="display: none;">
            <div class="card-header">
                <div class="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L13.89 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
                    </svg>
                </div>
                <h3 class="card-title">Extraction Progress</h3>
            </div>
            <div class="progress-content">
                <div id="progress-bar">
                    <span id="progress-bar-fill" style="width: 0%"></span>
                </div>
                <p id="progress-text">Fetched 0 reviews</p>
            </div>
        </div>

        <!-- Charts Section -->
        <div id="charts" class="charts-grid" style="display: none;">
            <div class="card glassmorphism chart-card">
                <div class="card-header">
                    <h3 class="card-title">Rating Distribution</h3>
                </div>
                <div class="chart-container">
                    <canvas id="rating-chart"></canvas>
                </div>
            </div>
            <div class="card glassmorphism chart-card">
                <div class="card-header">
                    <h3 class="card-title">Review Timeline</h3>
                </div>
                <div class="chart-container">
                    <canvas id="timeline-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Export Button -->
        <div id="export-container" style="display: none;">
            <button id="export-excel" class="btn-primary btn-export">📊 Export to Excel</button>
        </div>

        <!-- Reviews Container -->
        <div id="reviews-container" class="card glassmorphism" style="display: none;">
            <div class="card-header">
                <div class="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h2 class="card-title">Extracted Reviews</h2>
            </div>
            <div id="reviews-list"></div>
        </div>
    </div>

    <script>
        // Set default dates
        const today = new Date();
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        
        document.getElementById('start_date').valueAsDate = oneMonthAgo;
        document.getElementById('end_date').valueAsDate = today;

        // Form submission
        document.getElementById('review-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            
            // Reset charts and reviews
            if (ratingChart) ratingChart.destroy();
            if (timelineChart) timelineChart.destroy();
            document.getElementById('charts').style.display = 'none';
            document.getElementById('reviews-container').style.display = 'none';
            document.getElementById('reviews-list').innerHTML = '';
            
            const progressBar = document.getElementById('progress-bar-fill');
            const progressText = document.getElementById('progress-text');
            document.getElementById('progress-container').style.display = 'block';
            
            fetch('/get_reviews', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                return new ReadableStream({
                    start(controller) {
                        function push() {
                            reader.read().then(({ done, value }) => {
                                if (done) {
                                    controller.close();
                                    return;
                                }
                                buffer += decoder.decode(value, { stream: true });
                                const lines = buffer.split('\n');
                                buffer = lines.pop();
                                for (const line of lines) {
                                    if (line.trim().startsWith('data:')) {
                                        const data = JSON.parse(line.trim().slice(5));
                                        if (data.progress) {
                                            updateProgress(data.total, data.current);
                                        } else if (data.done) {
                                            updateCharts(data.rating_distribution, data.timeline);
                                            displayReviews(data.reviews);
                                            document.getElementById('export-container').style.display = 'block';
                                        }
                                    }
                                }
                                push();
                            });
                        }
                        push();
                    }
                });
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while fetching reviews. Please try again.');
                document.getElementById('progress-container').style.display = 'none';
            });
        });

        // Progress bar update
        function updateProgress(total, current) {
            const progressBar = document.getElementById('progress-bar-fill');
            const progressText = document.getElementById('progress-text');
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
            progressText.textContent = `Fetched ${current} of ${total} reviews (${percentage.toFixed(1)}%)`;
        }

        // Charts update
        let ratingChart, timelineChart;

        function updateCharts(ratingData, timelineData) {
            const ctx1 = document.getElementById('rating-chart').getContext('2d');
            const ctx2 = document.getElementById('timeline-chart').getContext('2d');

            // Destroy existing charts if they exist
            if (ratingChart) ratingChart.destroy();
            if (timelineChart) timelineChart.destroy();

            ratingChart = new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: Object.keys(ratingData),
                    datasets: [{
                        label: 'Number of Reviews',
                        data: Object.values(ratingData),
                        backgroundColor: 'rgba(75, 192, 192, 0.6)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Distribution of Ratings',
                            font: {
                                size: 20
                            }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Reviews',
                                font: {
                                    size: 14
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Rating',
                                font: {
                                    size: 14
                                }
                            }
                        }
                    }
                }
            });

            timelineChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: Object.keys(timelineData),
                    datasets: [{
                        label: 'Number of Reviews',
                        data: Object.values(timelineData),
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        fill: true,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Timeline of Review Submissions',
                            font: {
                                size: 20
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                precision: 0
                            },
                            title: {
                                display: true,
                                text: 'Number of Reviews',
                                font: {
                                    size: 14
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date',
                                font: {
                                    size: 14
                                }
                            }
                        }
                    }
                }
            });

            document.getElementById('charts').style.display = 'grid';
        }

        // Export to Excel
        document.getElementById('export-excel').addEventListener('click', function() {
            window.location.href = '/export_excel';
        });

        // Add this new function to display reviews
        function displayReviews(reviews) {
            const reviewsList = document.getElementById('reviews-list');
            reviewsList.innerHTML = '';
            reviews.forEach(review => {
                const reviewElement = document.createElement('div');
                reviewElement.className = 'review';
                reviewElement.innerHTML = `
                    <h3>${review.title}</h3>
                    <p><strong>Rating:</strong> ${review.rating}/5</p>
                    <p><strong>Date:</strong> ${review.date}</p>
                    <p><strong>Version:</strong> ${review.version}</p>
                    <p><strong>Author:</strong> ${review.author}</p>
                    <p>${review.content}</p>
                    <hr>
                `;
                reviewsList.appendChild(reviewElement);
            });
            document.getElementById('reviews-container').style.display = 'block';
        }

        // Theme switch functionality
        const toggleSwitch = document.querySelector('.theme-switch input[type="checkbox"]');
        const currentTheme = localStorage.getItem('theme');

        if (currentTheme) {
            document.documentElement.setAttribute('data-theme', currentTheme);
            if (currentTheme === 'dark') {
                toggleSwitch.checked = true;
                document.body.classList.add('dark-mode');
            }
        }

        function switchTheme(e) {
            if (e.target.checked) {
                document.documentElement.setAttribute('data-theme', 'dark');
                document.body.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                document.body.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
            }    
        }

        toggleSwitch.addEventListener('change', switchTheme, false);
        
        // Learn More functionality
        function showLearnMore() {
            alert('This tool extracts and analyzes Apple App Store reviews with advanced filtering capabilities, real-time progress tracking, interactive visualizations, and data export features. Perfect for app developers, marketers, and data analysts.');
        }
    </script>
</body>
</html>
